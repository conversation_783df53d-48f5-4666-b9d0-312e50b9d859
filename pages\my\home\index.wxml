<view class="home">
  <image class="top-bg-img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/my/my_bg.png" mode="widthFix" />
  <view class="sticky-header {{ isSticky?'bg-white':''}}" style="height: {{ navigationBarHeight }}px;"></view>
  <view class="my-home">
    <view class="header flex-item">
      <view class="left-area flex-item">
        <image wx:if="{{ isLogin }}" class="avartar" src="{{ baseInfo.portrait}}" mode="" />
        <image wx:else class="avartar" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_integrate/images/myhome/unlogin_user.png" mode="" />
        <view wx:if="{{ isLogin }}" class="text-area">
          <view class="name">{{ baseInfo.nickname }}</view>
          <view>{{ baseInfo.mobile }}</view>
        </view>
        <view wx:else class="text-area">
          <button-authorize isBindPhone="{{isLogin}}" bind:onAuthorize="getInfo">
            <view class="login-name">点击登录</view>
          </button-authorize>
        </view>
      </view>
      <!-- <view class="right-box flex-item" bind:tap="goFootHistory">
      <image class="image" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/my/my_zuji.png" mode="" />
      <view>浏览足迹</view>
    </view> -->
    </view>
    <view class="resume-area">
      <view class="left-text">完善你的简历，职位推荐更精准</view>
      <view class="right-btn" bind:tap="goResume">去完善</view>
      <van-circle class="circle-box" color="#FF6A4D" layer-color="#FFE9E4" line-cap="butt" value="{{resumeProgress}}" type="2d" stroke-width="4" size="20" />
      <image class="resume-bg" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/my/my_resume_bg.png" mode="" />
    </view>
    <!-- <view class="card-box">
    <view class="title">我的关注</view>
    <view class="bottom-area">
      <view class="list-item has-line" bind:tap="goFocus" data-id="1">
        <view class="num">8</view>
        <view class="text">公告</view>
      </view>
      <view class="list-item" bind:tap="goFocus" data-id="2">
        <view class="num">2</view>
        <view class="text">职位</view>
      </view>
    </view>
  </view>
  <view class="resume-box" bind:tap="goResume">
    <image class="box-image" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/my/my_jianli.png" mode="widthFix" />
    <view class="left">
      <view class="title-area">
        <view class="text">完善你的简历</view>
        <image class="img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/my/resume_arrow.png" mode="" />
      </view>
      <view class="sub-desc">完善度越高，职位推荐更精准</view>
    </view>
    <view class="right">
      <semi-circle-progress percentage="{{resumeProgress}}" />
    </view>
  </view> -->
  </view>
  <view class="tab-area" style="top: {{ navigationBarHeight }}px;">
    <view class="tab-main-area">
      <view class="tab-item {{activeTabIndex == index?'active':''}}" wx:for="{{tabList}}" bind:tap="changeIndex" data-index="{{index}}" wx:key="index">
        <view class="title">{{item.title}}</view>
      </view>
    </view>
    <view class="sub-tab-area" wx:if="{{tabList[activeTabIndex].subList.length}}">
      <view bind:tap="changeSubIndex" data-index="{{index}}" class="sub-tab-item {{subActiveTabIndex == index?'active':''}}" wx:for="{{tabList[activeTabIndex].subList}}" wx:key="index">
        {{item.title}}
      </view>
    </view>
  </view>
  <view class="tips-box" wx:if="{{tipsShow && activeTabIndex == 2}}">
    <view class="contain-area">
      <view class="text">{{tips}}</view>
      <image bind:tap="closetip" class="close-img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/job/comparison/close.png" mode="" />
    </view>
  </view>
  <!-- 根据吸顶状态决定滚动方式 -->
  <view class="scroll-list" wx:if="{{!isSticky}}">
    <!-- 未吸顶时：使用普通view，页面整体滚动 -->
    <view class="focus-area" wx:if="{{activeTabIndex != 2}}">
      <view class="list-area" wx:if="{{dataList.length}}">
        <!-- 公告列表 -->
        <view class="job-list mt32" wx:if="{{activeTabIndex == 0}}">
          <notice-foucus-card wx:for="{{dataList}}" wx:key="id" wx:for-item="notice" wx:for-index="noticeIndex" isEdit="{{isEdit}}" noticeData="{{notice}}" bind:selectNotice="onNoticeSelect" />
        </view>

        <!-- 职位列表 -->
        <view wx:if="{{activeTabIndex == 1}}" class="job-list-area">
          <view class="list-item" wx:for="{{ dataList }}" wx:key="index" wx:for-item="group" wx:for-index="groupIndex">
            <view class="title-text">{{group.title}}</view>
            <view class="job-list">
              <job-foucus-card wx:for="{{group.list}}" wx:key="id" wx:for-item="job" wx:for-index="jobIndex" isSort="{{ isSort }}" jobData="{{ job }}" groupIndex="{{ groupIndex }}" jobIndex="{{ jobIndex }}" topCanMove="{{ isSort && jobIndex > 0 }}" bottomCanMove="{{ isSort && jobIndex < group.list.length - 1 }}" bind:optionsClick="onJobOptionsClick" bind:moveUp="onMoveUp" bind:moveDown="onMoveDown" />
            </view>
          </view>
        </view>

        <!-- 第四个tab的内容 -->
        <view wx:if="{{activeTabIndex == 3}}" class="fourth-tab-area">
          <view class="placeholder-content">第四个Tab的内容区域</view>
        </view>
      </view>
      <empty-default wx:else imgurl="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/tabbar/focus_no_data.png" text="暂无数据"></empty-default>
    </view>
    <view class="foot-area" wx:if="{{activeTabIndex == 2}}">
      <view class="list-area" wx:if="{{dataList.length}}">
        <view class="list-item" wx:for="{{ dataList }}" wx:key="index">
          <view class="time-text">{{item.title}}</view>
          <notice-card list="{{item.list}}" wx:if="{{subActiveTabIndex == 0}}"></notice-card>
          <job-card list="{{item.list}}" isNewCard="{{ true }}" wx:else></job-card>
        </view>
      </view>
      <empty-default wx:else imgurl="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/tabbar/foot_no_data.png" text="暂无浏览足迹"></empty-default>
    </view>
  </view>

  <!-- 吸顶后：使用scroll-view组件 -->
  <scroll-view wx:else class="scroll-view-container" scroll-y="{{true}}" style="height: {{scrollViewHeight}}px;" bindscrolltolower="onScrollToLower" enhanced="{{true}}" show-scrollbar="{{false}}">
    <view class="focus-area" wx:if="{{activeTabIndex != 2}}">
      <view class="list-area" wx:if="{{dataList.length}}">
        <!-- 公告列表 -->
        <view class="job-list mt32" wx:if="{{activeTabIndex == 0}}">
          <notice-foucus-card wx:for="{{dataList}}" wx:key="id" wx:for-item="notice" wx:for-index="noticeIndex" isEdit="{{isEdit}}" noticeData="{{notice}}" bind:selectNotice="onNoticeSelect" />
        </view>

        <!-- 职位列表 -->
        <view wx:if="{{activeTabIndex == 1}}" class="job-list-area">
          <view class="list-item" wx:for="{{ dataList }}" wx:key="index" wx:for-item="group" wx:for-index="groupIndex">
            <view class="title-text">{{group.title}}</view>
            <view class="job-list">
              <job-foucus-card wx:for="{{group.list}}" wx:key="id" wx:for-item="job" wx:for-index="jobIndex" isSort="{{ isSort }}" jobData="{{ job }}" groupIndex="{{ groupIndex }}" jobIndex="{{ jobIndex }}" topCanMove="{{ isSort && jobIndex > 0 }}" bottomCanMove="{{ isSort && jobIndex < group.list.length - 1 }}" bind:optionsClick="onJobOptionsClick" bind:moveUp="onMoveUp" bind:moveDown="onMoveDown" />
            </view>
          </view>
        </view>

        <!-- 第四个tab的内容 -->
        <view wx:if="{{activeTabIndex == 3}}" class="fourth-tab-area">
          <view class="placeholder-content">第四个Tab的内容区域</view>
        </view>
      </view>
      <empty-default wx:else imgurl="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/tabbar/focus_no_data.png" text="暂无数据"></empty-default>
    </view>
    <view class="foot-area" wx:if="{{activeTabIndex == 2}}">
      <view class="list-area" wx:if="{{dataList.length}}">
        <view class="list-item" wx:for="{{ dataList }}" wx:key="index">
          <view class="time-text">{{item.title}}</view>
          <notice-card list="{{item.list}}" wx:if="{{subActiveTabIndex == 0}}"></notice-card>
          <job-card list="{{item.list}}" isNewCard="{{ true }}" wx:else></job-card>
        </view>
      </view>
      <empty-default wx:else imgurl="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/tabbar/foot_no_data.png" text="暂无浏览足迹"></empty-default>
    </view>
  </scroll-view>
</view>









<!-- 悬浮小窗口 -->

<view wx:if="{{ activeTabIndex == 1 }}" class="compare-ball" catch:tap="goComparison">
  <view class="img-area">
    <image class="img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/pk.png" mode="" />
    <view class="message" wx:if="{{pkListIds.length > 0}}">{{ pkListIds.length }}</view>
  </view>
  <view class="text">对比</view>
</view>




<!-- 底部导航栏 -->
<home-tabbar active="my" />

<!-- 一级弹窗：操作选项 -->
<action-selector visible="{{showOptionsPopup}}" options="{{optionsData}}" bind:select="onOptionsSelect" bind:close="closeOptionsPopup" bind:cancel="closeOptionsPopup" />

<!-- 二级弹窗：添加标签 -->
<action-selector visible="{{showTagPopup}}" options="{{tagOptionsData}}" bind:select="onTagSelect" bind:close="closeTagPopup" bind:cancel="closeTagPopup" />